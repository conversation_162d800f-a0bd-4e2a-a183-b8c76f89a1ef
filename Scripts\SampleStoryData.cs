using UnityEngine;
using UnityEngine.UIElements;

[CreateAssetMenu(fileName = "StoryData", menuName = "Visual Novel/Story Data")]
public class SampleStoryData : ScriptableObject
{
    [Header("Story Information")]
    public string storyTitle = "My Waifu Story";
    public string storyDescription = "A sample visual novel story";
    
    [<PERSON><PERSON>("Dialogue Data")]
    public DialogueData[] dialogues;

    [ContextMenu("Create Sample Story")]
    public void CreateSampleStory()
    {
        dialogues = new DialogueData[]
        {
            // Opening scene
            new DialogueData
            {
                characterName = "Narrator",
                dialogueText = "Welcome to the world of visual novels! This is a sample story to demonstrate the system.",
                backgroundImage = "taman paradise (1)",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 4f
            },
            
            // Character introduction
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Hello there! I'm <PERSON><PERSON>, your AI companion. Nice to meet you!",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimysenyummanis",
                characterPosition = "right",
                autoDelay = 3f,
                audioClip = "maimy_greeting"
            },

            new DialogueData
            {
                characterName = "Mai<PERSON>",
                dialogueText = "I've been waiting for someone like you to come along. Would you like to be my friend?",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimycuriouscute",
                characterPosition = "right",
                autoDelay = 4f
            },
            
            // First choice
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "So, what do you think? Will you be my friend?",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimymerenungmata",
                characterPosition = "right",
                isChoice = true,
                choices = new string[]
                {
                    "Of course! I'd love to be your friend!",
                    "I need to think about it...",
                    "Maybe we can start as acquaintances?"
                },
                choiceTargets = new int[] { 4, 7, 10 } // Jump to different dialogue indices
            },
            
            // Positive response path (index 4)
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Really?! That makes me so happy! I knew you were special!",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimy (1)",
                characterPosition = "right",
                autoDelay = 3f
            },

            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Let me show you around! There's so much I want to share with you!",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimy (2)",
                characterPosition = "center",
                autoDelay = 3f
            },

            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "This is going to be the start of a wonderful friendship!",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimy (3)",
                characterPosition = "center",
                autoDelay = 3f
            },
            
            // Hesitant response path (index 7)
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Oh... I understand. Take all the time you need.",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimyketawakecil",
                characterPosition = "right",
                autoDelay = 3f
            },

            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "I'll be here when you're ready. I believe good things come to those who wait.",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimymerenungmata",
                characterPosition = "right",
                autoDelay = 4f
            },

            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Until then, I hope we can at least talk sometimes?",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimycuriouscute",
                characterPosition = "right",
                autoDelay = 3f
            },
            
            // Neutral response path (index 10)
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Acquaintances... that's a good start! Everyone has to begin somewhere, right?",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimymerenungmata",
                characterPosition = "right",
                autoDelay = 4f
            },

            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "I'm patient. I believe that with time, we can become great friends!",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimysenyummanis",
                characterPosition = "right",
                autoDelay = 3f
            },

            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "For now, let me show you what I can do as your AI companion!",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimy (4)",
                characterPosition = "center",
                autoDelay = 3f
            },
            
            // Convergence point - all paths lead here
            new DialogueData
            {
                characterName = "System",
                dialogueText = "This concludes Maimy's story. Thank you for spending time with her!",
                backgroundImage = "taman paradise (1)",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 5f
            }
        };
        
        Debug.Log($"Created sample story with {dialogues.Length} dialogue entries!");
    }

    [ContextMenu("Validate Story Data")]
    public void ValidateStoryData()
    {
        if (dialogues == null || dialogues.Length == 0)
        {
            Debug.LogWarning("No dialogue data found!");
            return;
        }

        for (int i = 0; i < dialogues.Length; i++)
        {
            var dialogue = dialogues[i];
            
            // Check for empty essential fields
            if (string.IsNullOrEmpty(dialogue.characterName))
            {
                Debug.LogWarning($"Dialogue {i}: Missing character name");
            }
            
            if (string.IsNullOrEmpty(dialogue.dialogueText))
            {
                Debug.LogWarning($"Dialogue {i}: Missing dialogue text");
            }
            
            // Validate choice targets
            if (dialogue.isChoice && dialogue.choiceTargets != null)
            {
                foreach (int target in dialogue.choiceTargets)
                {
                    if (target >= dialogues.Length || target < 0)
                    {
                        Debug.LogWarning($"Dialogue {i}: Invalid choice target {target}");
                    }
                }
            }
        }
        
        Debug.Log("Story data validation complete!");
    }
}

// Helper class for creating dialogue data in the inspector
[System.Serializable]
public class DialogueDataHelper
{
    [Header("Basic Info")]
    public string characterName = "Character";
    [TextArea(3, 5)]
    public string dialogueText = "Enter dialogue here...";
    
    [Header("Visuals")]
    public string backgroundImage = "";
    public string characterImage = "";
    public CharacterPosition characterPosition = CharacterPosition.Right;
    
    [Header("Audio")]
    public string audioClip = "";
    
    [Header("Timing")]
    public float autoDelay = 3f;
    
    [Header("Choices (Optional)")]
    public bool isChoice = false;
    [TextArea(1, 3)]
    public string[] choices;
    public int[] choiceTargets;
    
    public DialogueData ToDialogueData()
    {
        return new DialogueData
        {
            characterName = this.characterName,
            dialogueText = this.dialogueText,
            backgroundImage = this.backgroundImage,
            characterImage = this.characterImage,
            characterPosition = this.characterPosition.ToString().ToLower(),
            audioClip = this.audioClip,
            autoDelay = this.autoDelay,
            isChoice = this.isChoice,
            choices = this.choices,
            choiceTargets = this.choiceTargets
        };
    }
}

public enum CharacterPosition
{
    Left,
    Center,
    Right
}

public class StorySceneController : MonoBehaviour
{
    [Header("Component References")]
    public DialogueSystem dialogueSystem;
    public CinematicEffects cinematicEffects;
    
    [Header("Input Settings")]
    public KeyCode nextKey = KeyCode.Space;
    public KeyCode autoKey = KeyCode.A;
    public KeyCode logKey = KeyCode.L;
    public KeyCode skipKey = KeyCode.S;
    public KeyCode menuKey = KeyCode.Escape;
    
    private UIDocument uiDocument;
    private VisualElement menuContainer;
    private bool isMenuOpen = false;

    private void Start()
    {
        InitializeController();
        StartStory();
    }

    private void InitializeController()
    {
        uiDocument = GetComponent<UIDocument>();
        
        if (dialogueSystem == null)
            dialogueSystem = GetComponent<DialogueSystem>();
        
        if (cinematicEffects == null)
            cinematicEffects = GetComponent<CinematicEffects>();
    }

    private void StartStory()
    {
        if (dialogueSystem != null)
        {
            dialogueSystem.StartDialogue();
        }
    }

    private void Update()
    {
        HandleInput();
    }

    private void HandleInput()
    {
        if (Input.GetKeyDown(nextKey) || Input.GetKeyDown(KeyCode.Return))
        {
            if (dialogueSystem != null && !isMenuOpen)
            {
                dialogueSystem.OnNextButtonClicked();
            }
        }

        if (Input.GetKeyDown(autoKey))
        {
            if (dialogueSystem != null)
            {
                dialogueSystem.ToggleAutoMode();
            }
        }

        if (Input.GetKeyDown(menuKey))
        {
            ToggleMenu();
        }
    }

    private void ToggleMenu()
    {
        isMenuOpen = !isMenuOpen;
        Debug.Log($"Menu: {(isMenuOpen ? "OPEN" : "CLOSED")}");
    }

    public void TriggerCameraShake()
    {
        if (cinematicEffects != null)
        {
            cinematicEffects.CameraShake();
        }
    }

    public void FadeOut(float duration = 1f)
    {
        if (cinematicEffects != null)
        {
            cinematicEffects.FadeOut(duration);
        }
    }

    public void FadeIn(float duration = 1f)
    {
        if (cinematicEffects != null)
        {
            cinematicEffects.FadeIn(duration);
        }
    }
}

