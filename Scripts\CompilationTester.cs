using UnityEngine;
using UnityEngine.UIElements;

/// <summary>
/// Simple script to test if all components compile and work together
/// This script verifies that all the fixer scripts are working properly
/// </summary>
public class CompilationTester : MonoBehaviour
{
    [Header("🧪 Compilation & Integration Tester")]
    [TextArea(3, 5)]
    public string info = "This script tests if all fixer components are working properly and can communicate with each other.";

    [Header("Test Results")]
    public bool allScriptsCompiled = false;
    public bool allComponentsFound = false;
    public bool integrationWorking = false;

    private void Start()
    {
        RunCompilationTests();
    }

    [ContextMenu("Run All Tests")]
    public void RunCompilationTests()
    {
        Debug.Log("🧪 Running compilation and integration tests...");
        
        // Test 1: Check if all scripts compiled
        TestScriptCompilation();
        
        // Test 2: Check if all components can be found
        TestComponentAvailability();
        
        // Test 3: Test integration between components
        TestComponentIntegration();
        
        // Summary
        ShowTestSummary();
    }

    private void TestScriptCompilation()
    {
        Debug.Log("📝 Testing script compilation...");
        
        try
        {
            // Try to create instances of all our custom types
            var dialogueData = new DialogueData();
            var quickFixer = typeof(QuickFixer);
            var brokenAssetFixer = typeof(BrokenAssetFixer);
            var inputSystemFixer = typeof(InputSystemFixer);
            var cinematicEffectsFixer = typeof(CinematicEffectsFixer);
            var dialogueSystemFixer = typeof(DialogueSystemFixer);
            
            allScriptsCompiled = true;
            Debug.Log("✅ All scripts compiled successfully!");
        }
        catch (System.Exception ex)
        {
            allScriptsCompiled = false;
            Debug.LogError($"❌ Script compilation failed: {ex.Message}");
        }
    }

    private void TestComponentAvailability()
    {
        Debug.Log("🔍 Testing component availability...");
        
        try
        {
            // Check if we can find or add components
            var dialogueSystem = GetComponent<DialogueSystem>() ?? gameObject.AddComponent<DialogueSystem>();
            var quickFixer = GetComponent<QuickFixer>() ?? gameObject.AddComponent<QuickFixer>();
            
            // Test UIDocument (this was causing the compilation error)
            var uiDocument = GetComponent<UIDocument>();
            if (uiDocument == null)
            {
                uiDocument = gameObject.AddComponent<UIDocument>();
                Debug.Log("✅ UIDocument component can be added");
            }
            
            allComponentsFound = true;
            Debug.Log("✅ All components are available and can be instantiated!");
        }
        catch (System.Exception ex)
        {
            allComponentsFound = false;
            Debug.LogError($"❌ Component availability test failed: {ex.Message}");
        }
    }

    private void TestComponentIntegration()
    {
        Debug.Log("🔗 Testing component integration...");
        
        try
        {
            // Test if QuickFixer can work with other components
            var quickFixer = GetComponent<QuickFixer>();
            if (quickFixer == null)
            {
                quickFixer = gameObject.AddComponent<QuickFixer>();
            }
            
            // Test if BrokenAssetFixer can work
            var assetFixer = GetComponent<BrokenAssetFixer>();
            if (assetFixer == null)
            {
                assetFixer = gameObject.AddComponent<BrokenAssetFixer>();
            }
            
            // Test basic functionality
            if (quickFixer != null && assetFixer != null)
            {
                integrationWorking = true;
                Debug.Log("✅ Component integration working!");
            }
        }
        catch (System.Exception ex)
        {
            integrationWorking = false;
            Debug.LogError($"❌ Component integration test failed: {ex.Message}");
        }
    }

    private void ShowTestSummary()
    {
        Debug.Log(@"
🧪 TEST SUMMARY
===============");
        
        Debug.Log($"Script Compilation: {(allScriptsCompiled ? "✅ PASS" : "❌ FAIL")}");
        Debug.Log($"Component Availability: {(allComponentsFound ? "✅ PASS" : "❌ FAIL")}");
        Debug.Log($"Component Integration: {(integrationWorking ? "✅ PASS" : "❌ FAIL")}");
        
        if (allScriptsCompiled && allComponentsFound && integrationWorking)
        {
            Debug.Log("🎉 ALL TESTS PASSED! Your project is ready to use.");
        }
        else
        {
            Debug.LogWarning("⚠️ Some tests failed. Check the errors above for details.");
        }
    }

    [ContextMenu("Test UIDocument Specifically")]
    public void TestUIDocumentSpecifically()
    {
        Debug.Log("🎨 Testing UIDocument specifically...");
        
        try
        {
            // This was the line causing the compilation error
            var uiDocument = GetComponent<UIDocument>();
            
            if (uiDocument == null)
            {
                uiDocument = gameObject.AddComponent<UIDocument>();
                Debug.Log("✅ UIDocument component added successfully");
            }
            else
            {
                Debug.Log("✅ UIDocument component already exists");
            }
            
            // Test UIDocument properties
            Debug.Log($"UIDocument Visual Tree Asset: {(uiDocument.visualTreeAsset != null ? "Assigned" : "Not assigned")}");
            Debug.Log($"UIDocument Root Element: {(uiDocument.rootVisualElement != null ? "Available" : "Not available")}");
            
            Debug.Log("✅ UIDocument test completed successfully!");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"❌ UIDocument test failed: {ex.Message}");
        }
    }

    [ContextMenu("Quick Fix Everything")]
    public void QuickFixEverything()
    {
        Debug.Log("🔧 Running quick fix for everything...");
        
        var quickFixer = GetComponent<QuickFixer>();
        if (quickFixer == null)
        {
            quickFixer = gameObject.AddComponent<QuickFixer>();
        }
        
        quickFixer.FixAllProblems();
    }
}
