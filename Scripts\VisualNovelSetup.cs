using UnityEngine;
using UnityEngine.UIElements;

[System.Serializable]
public class VisualNovelSetup : MonoBeh<PERSON>our
{
    [Header("Setup Instructions")]
    [TextArea(10, 20)]
    public string setupInstructions = @"
VISUAL NOVEL SETUP GUIDE
========================

1. SCENE SETUP:
   - Add this script to a GameObject in your story scene
   - Make sure you have a UIDocument component on the same GameObject
   - Assign the StoryPage.uxml file to the UIDocument

2. REQUIRED SCRIPTS:
   - DialogueSystem.cs (handles dialogue flow)
   - StorySceneController.cs (manages scene controls)
   - CinematicEffects.cs (premium visual effects)

3. FOLDER STRUCTURE:
   Assets/
   ├── Resources/
   │   ├── Background/     (put background images here)
   │   ├── characters/     (put character sprites here)
   │   └── Audio/          (put voice clips here)
   ├── Scripts/
   ├── UI/
   └── Scenes/

4. CREATING STORY DATA:
   - Right-click in Project → Create → Visual Novel → Story Data
   - Use the context menu 'Create Sample Story' to generate example data
   - Customize the dialogue array for your story

5. COMPONENT SETUP:
   - Add DialogueSystem, StorySceneController, and CinematicEffects to your story GameObject
   - Assign the story data to DialogueSystem.dialogues array
   - Configure settings in each component as needed

6. TESTING:
   - Play the scene
   - Use SPACE or ENTER to advance dialogue
   - Use A for Auto mode, L for Log, S for Skip, ESC for Menu
   - Click buttons or use keyboard shortcuts

7. CUSTOMIZATION:
   - Modify StoryPage.uss for visual styling
   - Add your own background images and character sprites
   - Record voice lines and add them to Resources/Audio/
   - Adjust timing and effects in the inspector
";

    [Header("Auto Setup")]
    public bool autoSetupOnStart = false;
    public SampleStoryData storyData;

    private void Start()
    {
        if (autoSetupOnStart)
        {
            AutoSetup();
        }
        else
        {
            Debug.Log(setupInstructions);
        }
    }

    [ContextMenu("Auto Setup Visual Novel")]
    public void AutoSetup()
    {
        Debug.Log("Setting up Visual Novel components...");

        // Get or add required components
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            dialogueSystem = gameObject.AddComponent<DialogueSystem>();
            Debug.Log("Added DialogueSystem component");
        }

        var storyController = GetComponent<StorySceneController>();
        if (storyController == null)
        {
            storyController = gameObject.AddComponent<StorySceneController>();
            Debug.Log("Added StorySceneController component");
        }

        var cinematicEffects = GetComponent<CinematicEffects>();
        if (cinematicEffects == null)
        {
            cinematicEffects = gameObject.AddComponent<CinematicEffects>();
            Debug.Log("Added CinematicEffects component");
        }

        // Link components
        storyController.dialogueSystem = dialogueSystem;
        storyController.cinematicEffects = cinematicEffects;

        // Setup story data if available
        if (storyData != null && storyData.dialogues != null)
        {
            dialogueSystem.dialogues = storyData.dialogues;
            Debug.Log($"Loaded story data with {storyData.dialogues.Length} dialogues");
        }
        else
        {
            Debug.LogWarning("No story data assigned! Create a SampleStoryData asset and assign it.");
        }

        // Check UIDocument
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            Debug.LogError("UIDocument component missing! Add one and assign StoryPage.uxml");
        }
        else if (uiDocument.visualTreeAsset == null)
        {
            Debug.LogWarning("No UXML file assigned to UIDocument! Assign StoryPage.uxml");
        }

        Debug.Log("Visual Novel setup complete!");
    }

    [ContextMenu("Validate Setup")]
    public void ValidateSetup()
    {
        Debug.Log("Validating Visual Novel setup...");

        bool isValid = true;

        // Check components
        if (GetComponent<DialogueSystem>() == null)
        {
            Debug.LogError("Missing DialogueSystem component!");
            isValid = false;
        }

        if (GetComponent<StorySceneController>() == null)
        {
            Debug.LogError("Missing StorySceneController component!");
            isValid = false;
        }

        if (GetComponent<CinematicEffects>() == null)
        {
            Debug.LogWarning("Missing CinematicEffects component (optional but recommended)");
        }

        // Check UIDocument
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            Debug.LogError("Missing UIDocument component!");
            isValid = false;
        }
        else if (uiDocument.visualTreeAsset == null)
        {
            Debug.LogError("No UXML file assigned to UIDocument!");
            isValid = false;
        }

        // Check story data
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem != null)
        {
            if (dialogueSystem.dialogues == null || dialogueSystem.dialogues.Length == 0)
            {
                Debug.LogWarning("No dialogue data assigned to DialogueSystem!");
            }
        }

        // Check folder structure
        CheckResourceFolders();

        if (isValid)
        {
            Debug.Log("✓ Visual Novel setup is valid!");
        }
        else
        {
            Debug.LogError("✗ Visual Novel setup has errors that need to be fixed.");
        }
    }

    private void CheckResourceFolders()
    {
        string[] requiredFolders = {
            "Resources",
            "Resources/Background",
            "Resources/characters",
            "Resources/Audio"
        };

        foreach (string folder in requiredFolders)
        {
            if (!System.IO.Directory.Exists(Application.dataPath + "/" + folder))
            {
                Debug.LogWarning($"Recommended folder missing: Assets/{folder}");
            }
        }
    }

    [ContextMenu("Create Sample Resources")]
    public void CreateSampleResources()
    {
        Debug.Log("Creating sample resource folders...");

        string[] folders = {
            "Resources",
            "Resources/Background",
            "Resources/characters",
            "Resources/Audio"
        };

        foreach (string folder in folders)
        {
            string fullPath = Application.dataPath + "/" + folder;
            if (!System.IO.Directory.Exists(fullPath))
            {
                System.IO.Directory.CreateDirectory(fullPath);
                Debug.Log($"Created folder: Assets/{folder}");
            }
        }

        // Create placeholder files
        CreatePlaceholderFile("Resources/Background/README.txt", 
            "Place your background images here (PNG/JPG)\nExample: school_hallway.png, tech_lab.jpg");
        
        CreatePlaceholderFile("Resources/characters/README.txt", 
            "Place your character sprites here (PNG with transparency)\nExample: maimy_happy.png, maimy_sad.png");
        
        CreatePlaceholderFile("Resources/Audio/README.txt", 
            "Place your voice clips here (WAV/MP3)\nExample: maimy_greeting.wav");

        Debug.Log("Sample resource structure created!");
        
        #if UNITY_EDITOR
        UnityEditor.AssetDatabase.Refresh();
        #endif
    }

    private void CreatePlaceholderFile(string relativePath, string content)
    {
        string fullPath = Application.dataPath + "/" + relativePath;
        if (!System.IO.File.Exists(fullPath))
        {
            System.IO.File.WriteAllText(fullPath, content);
        }
    }

    [ContextMenu("Show Keyboard Shortcuts")]
    public void ShowKeyboardShortcuts()
    {
        Debug.Log(@"
VISUAL NOVEL KEYBOARD SHORTCUTS
===============================

SPACE / ENTER  - Next dialogue
A              - Toggle Auto mode
L              - Show dialogue Log
S              - Skip dialogue
ESC            - Open/Close Menu

MOUSE
=====
Click Next button - Advance dialogue
Click Choice buttons - Make selection
Mouse movement - Parallax effect (if enabled)

MENU OPTIONS
============
Resume    - Close menu and continue
Save      - Save game progress (not implemented yet)
Load      - Load saved game (not implemented yet)
Settings  - Game settings (not implemented yet)
Main Menu - Return to main menu
");
    }
}
