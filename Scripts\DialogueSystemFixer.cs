using UnityEngine;
using UnityEngine.UIElements;

[System.Serializable]
public class DialogueSystemFixer : MonoBeh<PERSON><PERSON>
{
    [<PERSON><PERSON>("Auto-Fix Settings")]
    [Tooltip("Automatically fix issues on Start")]
    public bool autoFixOnStart = true;
    
    [Header("Story Data")]
    [Tooltip("Story data asset to assign if none is found")]
    public SampleStoryData fallbackStoryData;

    private void Start()
    {
        if (autoFixOnStart)
        {
            FixDialogueSystemIssues();
        }
    }

    [ContextMenu("Fix Dialogue System Issues")]
    public void FixDialogueSystemIssues()
    {
        Debug.Log("🔧 Starting Dialogue System Fix...");

        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            Debug.LogError("❌ No DialogueSystem component found!");
            return;
        }

        // Fix 1: Check and assign story data
        if (dialogueSystem.dialogues == null || dialogueSystem.dialogues.Length == 0)
        {
            Debug.Log("📝 No dialogue data found, attempting to load...");
            
            // Try to load from assigned fallback
            if (fallbackStoryData != null && fallbackStoryData.dialogues != null && fallbackStoryData.dialogues.Length > 0)
            {
                dialogueSystem.SetDialogueData(fallbackStoryData.dialogues);
                Debug.Log("✅ Loaded dialogue data from assigned fallback story data");
            }
            else
            {
                // Try to find MaimyStoryData in Resources
                var storyData = Resources.Load<SampleStoryData>("MaimyStoryData");
                if (storyData != null && storyData.dialogues != null && storyData.dialogues.Length > 0)
                {
                    dialogueSystem.SetDialogueData(storyData.dialogues);
                    Debug.Log("✅ Loaded dialogue data from Resources/MaimyStoryData");
                }
                else
                {
                    // Create minimal test data as last resort
                    CreateMinimalTestData(dialogueSystem);
                }
            }
        }

        // Fix 2: Ensure UIDocument is properly set up
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            uiDocument = gameObject.AddComponent<UIDocument>();
            Debug.Log("✅ Added missing UIDocument component");
        }

        if (uiDocument.visualTreeAsset == null)
        {
            var uxml = Resources.Load<VisualTreeAsset>("UI/StoryPage");
            if (uxml == null)
                uxml = Resources.Load<VisualTreeAsset>("StoryPage");
            
            if (uxml != null)
            {
                uiDocument.visualTreeAsset = uxml;
                Debug.Log("✅ Assigned StoryPage.uxml to UIDocument");
            }
            else
            {
                Debug.LogWarning("⚠️ Could not find StoryPage.uxml in Resources");
            }
        }

        // Fix 3: Validate setup
        ValidateSetup(dialogueSystem);

        Debug.Log("✅ Dialogue System Fix Complete!");
    }

    private void CreateMinimalTestData(DialogueSystem dialogueSystem)
    {
        Debug.Log("📝 Creating minimal test dialogue data...");
        
        var testDialogues = new DialogueData[]
        {
            new DialogueData
            {
                characterName = "System",
                dialogueText = "Welcome! This is a test dialogue to prevent errors.",
                backgroundImage = "",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 3f
            },
            new DialogueData
            {
                characterName = "System",
                dialogueText = "Please assign proper story data to the DialogueSystem component.",
                backgroundImage = "",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 3f
            }
        };

        dialogueSystem.SetDialogueData(testDialogues);
        Debug.Log("✅ Created minimal test dialogue data");
    }

    private void ValidateSetup(DialogueSystem dialogueSystem)
    {
        Debug.Log("🔍 Validating DialogueSystem setup...");

        bool isValid = true;

        // Check dialogue data
        if (dialogueSystem.dialogues == null || dialogueSystem.dialogues.Length == 0)
        {
            Debug.LogError("❌ No dialogue data assigned!");
            isValid = false;
        }
        else
        {
            Debug.Log($"✅ Dialogue data: {dialogueSystem.dialogues.Length} dialogues loaded");
            
            // Validate each dialogue
            for (int i = 0; i < dialogueSystem.dialogues.Length; i++)
            {
                var dialogue = dialogueSystem.dialogues[i];
                if (dialogue == null)
                {
                    Debug.LogWarning($"⚠️ Dialogue {i} is null");
                    continue;
                }

                if (string.IsNullOrEmpty(dialogue.dialogueText))
                {
                    Debug.LogWarning($"⚠️ Dialogue {i} has empty text");
                }

                // Validate choice targets
                if (dialogue.isChoice && dialogue.choiceTargets != null)
                {
                    foreach (int target in dialogue.choiceTargets)
                    {
                        if (target < 0 || target >= dialogueSystem.dialogues.Length)
                        {
                            Debug.LogWarning($"⚠️ Dialogue {i} has invalid choice target: {target}");
                        }
                    }
                }
            }
        }

        // Check UIDocument
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            Debug.LogError("❌ Missing UIDocument component!");
            isValid = false;
        }
        else if (uiDocument.visualTreeAsset == null)
        {
            Debug.LogError("❌ UIDocument missing UXML file!");
            isValid = false;
        }
        else
        {
            Debug.Log("✅ UIDocument properly configured");
        }

        if (isValid)
        {
            Debug.Log("✅ All validation checks passed!");
        }
        else
        {
            Debug.LogWarning("⚠️ Some validation checks failed. Please review the errors above.");
        }
    }

    [ContextMenu("Show Current Dialogue Info")]
    public void ShowCurrentDialogueInfo()
    {
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem != null)
        {
            Debug.Log($"Current Dialogue Info: {dialogueSystem.GetCurrentDialogueInfo()}");
        }
        else
        {
            Debug.LogError("No DialogueSystem component found!");
        }
    }
}
