using UnityEngine;

/// <summary>
/// Fixes CinematicEffects Input System conflicts
/// This script ensures CinematicEffects works properly with both old and new Input Systems
/// </summary>
public class CinematicEffectsFixer : MonoBehaviour
{
    [Header("🎬 CinematicEffects Input System Fix")]
    [TextArea(3, 5)]
    public string info = "This script fixes Input System conflicts in CinematicEffects by safely handling mouse input.";
    
    [Header("Settings")]
    public bool autoFixOnStart = true;
    public bool disableParallaxOnConflict = true;
    
    private CinematicEffects cinematicEffects;
    
    private void Start()
    {
        cinematicEffects = GetComponent<CinematicEffects>();
        
        if (autoFixOnStart)
        {
            FixCinematicEffectsConflicts();
        }
    }
    
    [ContextMenu("Fix CinematicEffects Input Conflicts")]
    public void FixCinematicEffectsConflicts()
    {
        Debug.Log("🎬 Fixing CinematicEffects Input System conflicts...");
        
        if (cinematicEffects == null)
        {
            cinematicEffects = GetComponent<CinematicEffects>();
        }
        
        if (cinematicEffects == null)
        {
            Debug.LogWarning("⚠️ No CinematicEffects component found!");
            return;
        }
        
        // Test if Input System conflicts exist
        if (HasInputSystemConflict())
        {
            if (disableParallaxOnConflict)
            {
                cinematicEffects.enableParallax = false;
                Debug.Log("✅ Disabled parallax effect to prevent Input System conflicts");
            }
            
            Debug.Log("💡 CinematicEffects updated to handle Input System conflicts safely");
        }
        else
        {
            Debug.Log("✅ No Input System conflicts detected in CinematicEffects");
        }
    }
    
    private bool HasInputSystemConflict()
    {
        try
        {
            // Try to access Input.mousePosition
            Vector2 testPosition = Input.mousePosition;
            return false; // No conflict
        }
        catch (System.InvalidOperationException)
        {
            return true; // Conflict detected
        }
    }
    
    [ContextMenu("Test Input System")]
    public void TestInputSystem()
    {
        Debug.Log("🧪 Testing Input System compatibility...");
        
        try
        {
            Vector2 mousePos = Input.mousePosition;
            Debug.Log($"✅ Old Input System works: Mouse at {mousePos}");
        }
        catch (System.InvalidOperationException ex)
        {
            Debug.LogWarning($"⚠️ Input System conflict: {ex.Message}");
            Debug.Log("💡 New Input System is active. CinematicEffects parallax will be disabled.");
        }
    }
    
    [ContextMenu("Enable Parallax (if safe)")]
    public void EnableParallaxIfSafe()
    {
        if (cinematicEffects == null)
        {
            cinematicEffects = GetComponent<CinematicEffects>();
        }
        
        if (cinematicEffects != null)
        {
            if (!HasInputSystemConflict())
            {
                cinematicEffects.enableParallax = true;
                Debug.Log("✅ Parallax enabled - no Input System conflicts detected");
            }
            else
            {
                Debug.LogWarning("⚠️ Cannot enable parallax - Input System conflict detected");
                Debug.Log("💡 Switch to 'Input Manager (Old)' in Project Settings to use parallax");
            }
        }
    }
    
    [ContextMenu("Disable Parallax")]
    public void DisableParallax()
    {
        if (cinematicEffects == null)
        {
            cinematicEffects = GetComponent<CinematicEffects>();
        }
        
        if (cinematicEffects != null)
        {
            cinematicEffects.enableParallax = false;
            Debug.Log("✅ Parallax disabled");
        }
    }
    
    [ContextMenu("Show Input System Info")]
    public void ShowInputSystemInfo()
    {
        Debug.Log(@"
🎮 INPUT SYSTEM INFORMATION
==========================

CURRENT STATUS:
- Testing Input.mousePosition access...
");
        
        try
        {
            Vector2 mousePos = Input.mousePosition;
            Debug.Log($"✅ OLD INPUT SYSTEM: Active and working (Mouse: {mousePos})");
            Debug.Log("💡 CinematicEffects parallax can be safely enabled");
        }
        catch (System.InvalidOperationException)
        {
            Debug.Log("⚠️ NEW INPUT SYSTEM: Active (Old Input disabled)");
            Debug.Log("💡 CinematicEffects parallax should be disabled to prevent errors");
            Debug.Log("🔧 To use parallax: Edit → Project Settings → Player → Active Input Handling → 'Input Manager (Old)'");
        }
    }
}
