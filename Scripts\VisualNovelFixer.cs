using UnityEngine;
using UnityEngine.UIElements;

public class VisualNovelFixer : MonoBehaviour
{
    [Header("Fix Console Errors")]
    [TextArea(5, 10)]
    public string instructions = @"
This script will fix all the console errors:
1. Create and assign SampleStoryData asset
2. Add UIDocument component with StoryPage.uxml
3. Fix ArgumentOutOfRangeException with bounds checking
4. Setup proper image loading for Maimy characters

Click 'Fix All Issues' button in the inspector or run in play mode.
";

    [Header("Story Data")]
    public SampleStoryData storyDataAsset;
    
    [Header("UI Assets")]
    public VisualTreeAsset storyPageUxml;
    public StyleSheet storyPageUss;

    private void Start()
    {
        // Add a small delay to ensure all components are initialized
        StartCoroutine(DelayedFix());
    }

    private System.Collections.IEnumerator DelayedFix()
    {
        yield return new WaitForSeconds(0.1f);
        FixAllIssues();
    }

    [ContextMenu("Fix All Issues")]
    public void FixAllIssues()
    {
        Debug.Log("🔧 Starting Visual Novel Fix Process...");
        
        // Step 1: Ensure all required components exist
        EnsureRequiredComponents();

        // Step 2: Setup UIDocument
        SetupUIDocument();

        // Step 3: Create and assign story data
        SetupStoryData();

        // Step 4: Add DialogueSystemFixer for additional safety
        EnsureDialogueSystemFixer();

        // Step 5: Validate setup
        ValidateSetup();
        
        Debug.Log("✅ Visual Novel Fix Process Complete!");
    }

    private void EnsureDialogueSystemFixer()
    {
        Debug.Log("🛡️ Adding DialogueSystemFixer for additional safety...");

        var fixer = GetComponent<DialogueSystemFixer>();
        if (fixer == null)
        {
            fixer = gameObject.AddComponent<DialogueSystemFixer>();
            Debug.Log("✅ Added DialogueSystemFixer component");
        }

        // Assign fallback story data if available
        if (storyDataAsset != null && fixer.fallbackStoryData == null)
        {
            fixer.fallbackStoryData = storyDataAsset;
            Debug.Log("✅ Assigned fallback story data to DialogueSystemFixer");
        }
    }

    private void EnsureRequiredComponents()
    {
        Debug.Log("📦 Ensuring required components...");
        
        // Add DialogueSystem if missing
        if (GetComponent<DialogueSystem>() == null)
        {
            gameObject.AddComponent<DialogueSystem>();
            Debug.Log("✅ Added DialogueSystem component");
        }
        
        // Add StorySceneController if missing
        if (GetComponent<StorySceneController>() == null)
        {
            gameObject.AddComponent<StorySceneController>();
            Debug.Log("✅ Added StorySceneController component");
        }
        
        // Add CinematicEffects if missing
        if (GetComponent<CinematicEffects>() == null)
        {
            gameObject.AddComponent<CinematicEffects>();
            Debug.Log("✅ Added CinematicEffects component");
        }
    }

    private void SetupUIDocument()
    {
        Debug.Log("🎨 Setting up UIDocument...");
        
        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            uiDocument = gameObject.AddComponent<UIDocument>();
            Debug.Log("✅ Added UIDocument component");
        }
        
        // Try to load StoryPage.uxml if not assigned
        if (uiDocument.visualTreeAsset == null)
        {
            var uxml = Resources.Load<VisualTreeAsset>("UI/StoryPage");
            if (uxml == null)
            {
                uxml = Resources.Load<VisualTreeAsset>("StoryPage");
            }
            
            if (uxml != null)
            {
                uiDocument.visualTreeAsset = uxml;
                Debug.Log("✅ Assigned StoryPage.uxml to UIDocument");
            }
            else
            {
                Debug.LogError("❌ Could not find StoryPage.uxml! Make sure it's in Resources folder or assign manually.");
            }
        }
    }

    private void SetupStoryData()
    {
        Debug.Log("📚 Setting up story data...");
        
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null) return;
        
        // If no story data assigned, try to find or create one
        if (storyDataAsset == null)
        {
            // Try to load existing story data
            storyDataAsset = Resources.Load<SampleStoryData>("MaimyStoryData");
            
            if (storyDataAsset == null)
            {
                Debug.LogWarning("⚠️ No story data found. Creating sample data...");
                CreateSampleStoryData();
            }
        }
        
        // Assign story data to dialogue system
        if (storyDataAsset != null && storyDataAsset.dialogues != null)
        {
            dialogueSystem.dialogues = storyDataAsset.dialogues;
            Debug.Log($"✅ Assigned story data with {storyDataAsset.dialogues.Length} dialogues");
        }
        
        // Link components
        var storyController = GetComponent<StorySceneController>();
        if (storyController != null)
        {
            storyController.dialogueSystem = dialogueSystem;
            storyController.cinematicEffects = GetComponent<CinematicEffects>();
            Debug.Log("✅ Linked story controller components");
        }
    }

    private void CreateSampleStoryData()
    {
        // Create a temporary story data in memory
        var tempStoryData = ScriptableObject.CreateInstance<SampleStoryData>();
        tempStoryData.CreateSampleStory();
        
        // Update with real Maimy images
        UpdateStoryDataWithMaimyImages(tempStoryData);
        
        storyDataAsset = tempStoryData;
        Debug.Log("✅ Created temporary story data with Maimy images");
    }

    private void UpdateStoryDataWithMaimyImages(SampleStoryData storyData)
    {
        if (storyData.dialogues == null) return;
        
        // Update image paths to use actual Maimy images
        for (int i = 0; i < storyData.dialogues.Length; i++)
        {
            var dialogue = storyData.dialogues[i];
            
            // Update background images
            if (!string.IsNullOrEmpty(dialogue.backgroundImage))
            {
                if (dialogue.backgroundImage.Contains("school") || dialogue.backgroundImage.Contains("tech"))
                {
                    dialogue.backgroundImage = "taman paradise (1)";
                }
                else if (dialogue.backgroundImage.Contains("courtyard"))
                {
                    dialogue.backgroundImage = "taman paradise (1)";
                }
            }
            
            // Update character images with actual Maimy images
            if (!string.IsNullOrEmpty(dialogue.characterImage))
            {
                switch (dialogue.characterImage)
                {
                    case "maimy_happy":
                        dialogue.characterImage = "maimysenyummanis";
                        break;
                    case "maimy_shy":
                    case "maimy_hopeful":
                        dialogue.characterImage = "maimycuriouscute";
                        break;
                    case "maimy_excited":
                        dialogue.characterImage = "maimy (1)";
                        break;
                    case "maimy_sad":
                        dialogue.characterImage = "maimyketawakecil";
                        break;
                    case "maimy_understanding":
                    case "maimy_thoughtful":
                        dialogue.characterImage = "maimymerenungmata";
                        break;
                    case "maimy_confident":
                        dialogue.characterImage = "maimysenyummanis";
                        break;
                }
            }
        }
    }

    private void ValidateSetup()
    {
        Debug.Log("🔍 Validating setup...");
        
        bool isValid = true;
        
        // Check components
        if (GetComponent<DialogueSystem>() == null)
        {
            Debug.LogError("❌ Missing DialogueSystem!");
            isValid = false;
        }
        
        if (GetComponent<UIDocument>() == null)
        {
            Debug.LogError("❌ Missing UIDocument!");
            isValid = false;
        }
        else if (GetComponent<UIDocument>().visualTreeAsset == null)
        {
            Debug.LogError("❌ UIDocument missing UXML file!");
            isValid = false;
        }
        
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem != null && (dialogueSystem.dialogues == null || dialogueSystem.dialogues.Length == 0))
        {
            Debug.LogError("❌ No dialogue data assigned!");
            isValid = false;
        }
        
        if (isValid)
        {
            Debug.Log("✅ All issues fixed! Visual Novel should work properly now.");
        }
        else
        {
            Debug.LogError("❌ Some issues remain. Check the errors above.");
        }
    }
}
