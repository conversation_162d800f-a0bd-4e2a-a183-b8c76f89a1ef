<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/UI/StoryPage.uss" />
    
    <!-- Main Story Container -->
    <ui:VisualElement name="story-container" class="story-container">
        
        <!-- Background Image Container -->
        <ui:VisualElement name="background-image" class="background-image" />
        
        <!-- Character Display Area -->
        <ui:VisualElement name="character-container" class="character-container">
            <ui:VisualElement name="character-image" class="character-image" />
        </ui:VisualElement>
        
        <!-- UI Overlay Container -->
        <ui:VisualElement name="ui-overlay" class="ui-overlay">
            
            <!-- Top UI Bar -->
            <ui:VisualElement name="top-bar" class="top-bar">
                <ui:Button name="menu-button" text="Menu" class="top-button" />
                <ui:Button name="auto-button" text="Auto" class="top-button" />
                <ui:Button name="skip-button" text="Skip" class="top-button" />
                <ui:Button name="log-button" text="Log" class="top-button" />
            </ui:VisualElement>
            
            <!-- Main Dialogue Area -->
            <ui:VisualElement name="dialogue-area" class="dialogue-area">
                
                <!-- Character Name Box -->
                <ui:VisualElement name="name-box" class="name-box">
                    <ui:Label name="character-name" text="Character Name" class="character-name" />
                </ui:VisualElement>
                
                <!-- Dialogue Text Box -->
                <ui:VisualElement name="dialogue-box" class="dialogue-box">
                    <ui:Label name="dialogue-text" text="This is where the dialogue text will appear. It can be multiple lines and will support text animation effects." class="dialogue-text" />
                    
                    <!-- Next Button -->
                    <ui:Button name="next-button" text="▶" class="next-button" />
                </ui:VisualElement>
                
                <!-- Choices Container -->
                <ui:VisualElement name="choices-container" class="choices-container" style="display: none;" />
                
            </ui:VisualElement>
            
            <!-- Bottom UI Bar -->
            <ui:VisualElement name="bottom-bar" class="bottom-bar">
                <ui:Button name="save-button" text="Save" class="bottom-button" />
                <ui:Button name="load-button" text="Load" class="bottom-button" />
                <ui:Button name="settings-button" text="Settings" class="bottom-button" />
            </ui:VisualElement>
            
        </ui:VisualElement>
        
        <!-- Fade Overlay for Transitions -->
        <ui:VisualElement name="fade-overlay" class="fade-overlay" style="display: none;" />
        
    </ui:VisualElement>
</ui:UXML>
