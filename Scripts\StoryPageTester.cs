using UnityEngine;
using UnityEngine.UIElements;

[System.Serializable]
public class StoryPageTester : MonoBehaviour
{
    [Head<PERSON>("Story Page Layout Tester")]
    [TextArea(5, 10)]
    public string instructions = @"
This script tests the new Story Page layout with:
- Background images
- Character positioning (left, center, right)
- Dialog box with character name
- Interactive buttons (<PERSON>, Skip, Menu, etc.)
- Choice system
- Fade transitions

The layout includes:
✅ Background image container
✅ Character image with positioning
✅ Dialog box with name plate
✅ Next button with hover effects
✅ Top UI bar (Menu, Auto, Skip, Log)
✅ Bottom UI bar (Save, Load, Settings)
✅ Choices container
✅ Fade overlay for transitions

Click 'Test Layout' to see the story page in action!
";

    [Header("Test Settings")]
    public bool autoStartTest = true;
    public float testDelay = 1f;

    private DialogueSystem dialogueSystem;

    private void Start()
    {
        if (autoStartTest)
        {
            Invoke(nameof(TestLayout), testDelay);
        }
    }

    [ContextMenu("Test Layout")]
    public void TestLayout()
    {
        Debug.Log("🎭 Testing Story Page Layout...");

        // Get or add DialogueSystem
        dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            dialogueSystem = gameObject.AddComponent<DialogueSystem>();
            Debug.Log("✅ Added DialogueSystem component");
        }

        // Create test dialogue data
        CreateTestDialogueData();

        // Start the dialogue to test the layout
        dialogueSystem.StartDialogue();

        Debug.Log("🎨 Story Page Layout Test Started!");
        Debug.Log("📝 Use the Next button or Space/Enter to advance dialogue");
        Debug.Log("🎮 Try the Auto, Skip, Menu buttons in the top bar");
        Debug.Log("💾 Try the Save, Load, Settings buttons in the bottom bar");
    }

    private void CreateTestDialogueData()
    {
        var testDialogues = new DialogueData[]
        {
            new DialogueData
            {
                characterName = "Narrator",
                dialogueText = "Welcome to the enhanced Story Page layout! This demonstrates the new visual novel interface with background images, character positioning, and interactive UI elements.",
                backgroundImage = "taman paradise (1)",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Hi there! I'm Maimy, your AI companion. Notice how I appear on the screen with smooth transitions and positioning effects.",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimysenyummanis",
                characterPosition = "center",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "I can appear in different positions! Right now I'm in the center, but I can also move to the left or right side of the screen.",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimycuriouscute",
                characterPosition = "left",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "See? Now I'm on the right side! The character positioning system allows for dynamic scene composition.",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimyketawakecil",
                characterPosition = "right",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "System",
                dialogueText = "The layout includes interactive buttons in the top bar (Menu, Auto, Skip, Log) and bottom bar (Save, Load, Settings). Try clicking them!",
                backgroundImage = "kota",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 5f
            },
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "What would you like to do next? This demonstrates the choice system with multiple options.",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimymerenungmata",
                characterPosition = "center",
                autoDelay = 3f,
                isChoice = true,
                choices = new string[]
                {
                    "Continue the story",
                    "Learn more about the interface",
                    "Test different backgrounds"
                },
                choiceTargets = new int[] { 6, 7, 8 }
            },
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Great choice! The story continues with beautiful backgrounds and character expressions. The layout adapts to different content seamlessly.",
                backgroundImage = "taman paradise (2)",
                characterImage = "maimysenyummanis",
                characterPosition = "center",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "System",
                dialogueText = "The interface features: responsive design, smooth transitions, hover effects, character positioning, background management, and a complete UI toolkit for visual novels.",
                backgroundImage = "maimystore1",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 5f
            },
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Look at these different backgrounds! The system supports various scene types - from stores to gardens to cityscapes.",
                backgroundImage = "maimystore2",
                characterImage = "maimycuriouscute",
                characterPosition = "left",
                autoDelay = 4f
            }
        };

        dialogueSystem.SetDialogueData(testDialogues);
        Debug.Log($"✅ Created test dialogue data with {testDialogues.Length} entries");
    }

    private void Update()
    {
        // Keyboard shortcuts for testing
        if (Input.GetKeyDown(KeyCode.Space) || Input.GetKeyDown(KeyCode.Return))
        {
            if (dialogueSystem != null)
                dialogueSystem.OnNextButtonClicked();
        }

        if (Input.GetKeyDown(KeyCode.A))
        {
            if (dialogueSystem != null)
                dialogueSystem.ToggleAutoMode();
        }

        if (Input.GetKeyDown(KeyCode.T))
        {
            TestLayout();
        }
    }

    private void OnGUI()
    {
        // Simple GUI for testing
        GUILayout.BeginArea(new Rect(10, 10, 300, 100));
        GUILayout.Label("Story Page Layout Tester", GUI.skin.box);
        GUILayout.Label("Press T to test layout");
        GUILayout.Label("Press Space/Enter to advance");
        GUILayout.Label("Press A to toggle Auto mode");
        GUILayout.EndArea();
    }
}
