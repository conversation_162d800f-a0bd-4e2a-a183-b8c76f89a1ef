using UnityEngine;
using UnityEngine.UIElements;

[System.Serializable]
public class StoryPageFixer : MonoBehaviour
{
    [<PERSON><PERSON>("🔧 Story Page Layout Fixer")]
    [TextArea(5, 10)]
    public string instructions = @"
MASALAH YANG TERDETEKSI:

❌ UIDocument tidak menggunakan StoryPage.uxml yang baru
❌ DialogueSystem tidak bisa load UI layout
❌ Story tidak dimainkan karena UI tidak ter-setup

SOLUSI:

✅ Assign StoryPage.uxml ke UIDocument
✅ Setup DialogueSystem dengan story data
✅ Tambah StoryPageTester untuk testing
✅ Start dialogue otomatis

Klik 'Fix Story Page' atau jalankan scene untuk auto-fix!
";

    [Header("Auto Fix Settings")]
    public bool autoFixOnStart = true;
    public bool addStoryPageTester = true;
    public bool startDialogueAutomatically = true;

    private void Start()
    {
        if (autoFixOnStart)
        {
            Invoke(nameof(FixStoryPage), 0.5f); // Delay sedikit untuk memastikan semua component loaded
        }
    }

    [ContextMenu("Fix Story Page")]
    public void FixStoryPage()
    {
        Debug.Log("🔧 Memperbaiki Story Page Layout...");

        // Step 1: Fix UIDocument
        FixUIDocument();

        // Step 2: Fix DialogueSystem
        FixDialogueSystem();

        // Step 3: Add StoryPageTester if needed
        if (addStoryPageTester)
        {
            AddStoryPageTester();
        }

        // Step 4: Start dialogue
        if (startDialogueAutomatically)
        {
            StartDialogue();
        }

        Debug.Log("✅ Story Page Layout berhasil diperbaiki!");
    }

    private void FixUIDocument()
    {
        Debug.Log("🎨 Memperbaiki UIDocument...");

        var uiDocument = GetComponent<UIDocument>();
        if (uiDocument == null)
        {
            uiDocument = gameObject.AddComponent<UIDocument>();
            Debug.Log("✅ Menambah UIDocument component");
        }

        // Load StoryPage.uxml dari Resources
        var storyPageUxml = Resources.Load<VisualTreeAsset>("StoryPage");
        if (storyPageUxml != null)
        {
            uiDocument.visualTreeAsset = storyPageUxml;
            Debug.Log("✅ StoryPage.uxml berhasil di-assign ke UIDocument");
        }
        else
        {
            Debug.LogError("❌ StoryPage.uxml tidak ditemukan di Resources folder!");
            Debug.LogError("💡 Pastikan file UI/StoryPage.uxml sudah di-copy ke Resources/StoryPage.uxml");
            return;
        }

        // Set panel settings if needed
        if (uiDocument.panelSettings == null)
        {
            var panelSettings = Resources.Load<PanelSettings>("UI Toolkit/PanelSettings");
            if (panelSettings == null)
            {
                // Try alternative path
                panelSettings = Resources.Load<PanelSettings>("PanelSettings");
            }
            
            if (panelSettings != null)
            {
                uiDocument.panelSettings = panelSettings;
                Debug.Log("✅ PanelSettings di-assign");
            }
        }
    }

    private void FixDialogueSystem()
    {
        Debug.Log("💬 Memperbaiki DialogueSystem...");

        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem == null)
        {
            dialogueSystem = gameObject.AddComponent<DialogueSystem>();
            Debug.Log("✅ Menambah DialogueSystem component");
        }

        // Check if dialogue data exists
        if (dialogueSystem.dialogues == null || dialogueSystem.dialogues.Length == 0)
        {
            // Try to load MaimyStoryData
            var storyData = Resources.Load<SampleStoryData>("MaimyStoryData");
            if (storyData != null && storyData.dialogues != null && storyData.dialogues.Length > 0)
            {
                dialogueSystem.SetDialogueData(storyData.dialogues);
                Debug.Log("✅ MaimyStoryData berhasil di-load");
            }
            else
            {
                // Create simple test data
                CreateTestDialogueData(dialogueSystem);
            }
        }
    }

    private void CreateTestDialogueData(DialogueSystem dialogueSystem)
    {
        Debug.Log("📝 Membuat test dialogue data...");

        var testDialogues = new DialogueData[]
        {
            new DialogueData
            {
                characterName = "System",
                dialogueText = "🎭 Story Page Layout berhasil diperbaiki! Sekarang kamu bisa melihat dialog box, background, dan character dengan benar.",
                backgroundImage = "taman paradise (1)",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Hai! Aku Maimy, AI companion kamu. Layout baru ini terlihat bagus kan? Sekarang aku bisa muncul dengan posisi yang berbeda-beda!",
                backgroundImage = "taman paradise (1)",
                characterImage = "maimysenyummanis",
                characterPosition = "center",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "Maimy",
                dialogueText = "Coba tekan tombol Auto, Skip, atau Menu di bagian atas. Atau tekan tombol Save, Load, Settings di bagian bawah!",
                backgroundImage = "kota",
                characterImage = "maimycuriouscute",
                characterPosition = "left",
                autoDelay = 4f
            },
            new DialogueData
            {
                characterName = "System",
                dialogueText = "Gunakan SPACE atau ENTER untuk melanjutkan dialog. Tekan A untuk toggle Auto mode. Semua fitur UI sudah berfungsi!",
                backgroundImage = "taman paradise (1)",
                characterImage = "",
                characterPosition = "center",
                autoDelay = 5f
            }
        };

        dialogueSystem.SetDialogueData(testDialogues);
        Debug.Log("✅ Test dialogue data berhasil dibuat");
    }

    private void AddStoryPageTester()
    {
        var tester = GetComponent<StoryPageTester>();
        if (tester == null)
        {
            tester = gameObject.AddComponent<StoryPageTester>();
            tester.autoStartTest = false; // Kita akan start manual
            Debug.Log("✅ StoryPageTester component ditambahkan");
        }
    }

    private void StartDialogue()
    {
        Debug.Log("🎬 Memulai dialogue...");

        // Wait a bit for UI to initialize
        Invoke(nameof(DelayedStartDialogue), 1f);
    }

    private void DelayedStartDialogue()
    {
        var dialogueSystem = GetComponent<DialogueSystem>();
        if (dialogueSystem != null)
        {
            dialogueSystem.StartDialogue();
            Debug.Log("✅ Dialogue berhasil dimulai!");
            Debug.Log("🎮 Gunakan SPACE/ENTER untuk melanjutkan dialog");
            Debug.Log("🎮 Gunakan A untuk toggle Auto mode");
        }
        else
        {
            Debug.LogError("❌ DialogueSystem tidak ditemukan!");
        }
    }

    private void Update()
    {
        // Emergency fix hotkey
        if (Input.GetKeyDown(KeyCode.F))
        {
            FixStoryPage();
        }

        // Manual start dialogue hotkey
        if (Input.GetKeyDown(KeyCode.R))
        {
            StartDialogue();
        }
    }

    private void OnGUI()
    {
        // Show fix instructions
        GUILayout.BeginArea(new Rect(10, Screen.height - 120, 400, 110));
        GUILayout.Box("Story Page Fixer", GUI.skin.box);
        GUILayout.Label("Press F to fix story page");
        GUILayout.Label("Press R to restart dialogue");
        GUILayout.Label("Press SPACE/ENTER to advance");
        GUILayout.EndArea();
    }
}
