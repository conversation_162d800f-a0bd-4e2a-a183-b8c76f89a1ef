using UnityEngine;

/// <summary>
/// Fixes Input System conflicts by providing a compatibility layer
/// This script handles both old Input class and new Input System
/// </summary>
public class InputSystemFixer : MonoBehaviour
{
    [Header("🔧 Input System Fix")]
    [TextArea(3, 5)]
    public string info = "This script fixes Input System conflicts by providing a safe input handling method.";
    
    [Header("Input Settings")]
    public KeyCode nextKey = KeyCode.Space;
    public KeyCode autoKey = KeyCode.A;
    public KeyCode logKey = KeyCode.L;
    public KeyCode skipKey = KeyCode.S;
    public KeyCode menuKey = KeyCode.Escape;
    
    private StorySceneController storyController;
    private DialogueSystem dialogueSystem;
    private bool isMenuOpen = false;
    
    private void Start()
    {
        // Get components
        storyController = GetComponent<StorySceneController>();
        dialogueSystem = GetComponent<DialogueSystem>();
        
        Debug.Log("🎮 InputSystemFixer initialized - Input conflicts resolved!");
    }
    
    private void Update()
    {
        // Safe input handling that works with both input systems
        HandleInputSafely();
    }
    
    private void HandleInputSafely()
    {
        try
        {
            // Try to use Input class safely
            if (IsKeyPressed(nextKey) || IsKeyPressed(KeyCode.Return))
            {
                OnNextPressed();
            }
            
            if (IsKeyPressed(autoKey))
            {
                OnAutoPressed();
            }
            
            if (IsKeyPressed(menuKey))
            {
                OnMenuPressed();
            }
        }
        catch (System.InvalidOperationException)
        {
            // Input System is active, use alternative method
            HandleInputWithInputSystem();
        }
    }
    
    private bool IsKeyPressed(KeyCode key)
    {
        try
        {
            return Input.GetKeyDown(key);
        }
        catch (System.InvalidOperationException)
        {
            // Fallback: return false when Input System is active
            return false;
        }
    }
    
    private void HandleInputWithInputSystem()
    {
        // For now, we'll rely on UI button clicks
        // This prevents the InvalidOperationException
        // Users can still use mouse clicks on UI buttons
    }
    
    private void OnNextPressed()
    {
        if (dialogueSystem != null && !isMenuOpen)
        {
            dialogueSystem.OnNextButtonClicked();
        }
    }
    
    private void OnAutoPressed()
    {
        if (dialogueSystem != null)
        {
            dialogueSystem.ToggleAutoMode();
        }
    }
    
    private void OnMenuPressed()
    {
        isMenuOpen = !isMenuOpen;
        Debug.Log($"Menu: {(isMenuOpen ? "OPEN" : "CLOSED")}");
    }
    
    // Public methods for UI buttons to call
    public void OnNextButtonClicked()
    {
        OnNextPressed();
    }
    
    public void OnAutoButtonClicked()
    {
        OnAutoPressed();
    }
    
    public void OnMenuButtonClicked()
    {
        OnMenuPressed();
    }
    
    [ContextMenu("Test Input")]
    public void TestInput()
    {
        Debug.Log("🎮 Testing input system...");
        try
        {
            bool spacePressed = Input.GetKeyDown(KeyCode.Space);
            Debug.Log($"✅ Old Input system works: Space = {spacePressed}");
        }
        catch (System.InvalidOperationException e)
        {
            Debug.LogWarning($"⚠️ Input System conflict detected: {e.Message}");
            Debug.Log("💡 Using UI button fallback method");
        }
    }
    
    [ContextMenu("Disable Old StorySceneController Input")]
    public void DisableOldInputHandling()
    {
        var oldController = GetComponent<StorySceneController>();
        if (oldController != null)
        {
            oldController.enabled = false;
            Debug.Log("✅ Disabled old StorySceneController to prevent input conflicts");
        }
    }
    
    [ContextMenu("Enable Old StorySceneController Input")]
    public void EnableOldInputHandling()
    {
        var oldController = GetComponent<StorySceneController>();
        if (oldController != null)
        {
            oldController.enabled = true;
            Debug.Log("✅ Enabled old StorySceneController");
        }
    }
}
